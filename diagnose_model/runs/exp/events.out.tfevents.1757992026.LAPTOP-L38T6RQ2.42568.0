H       ��H�	n�52�A
brain.Event:2R.
,tensorboard.summary.writer.event_file_writerwF!       ��2	|T@52�A*


Loss/train��?�6�       ���	|T@52�A*

Loss/val5��>u���"       x=�	|T@52�A*

Accuracy/train��B���        )�P	|T@52�A*

Accuracy/valﱸB7ǩ�!       {��	|T@52�A*


Learning Rateo�:p}��        )�P	��A52�A*


Loss/train�/�>]��       ��2	��A52�A*

Loss/val��>d�a$       B+�M	��A52�A*

Accuracy/train���B��"       x=�	��A52�A*

Accuracy/val�M�B^��#       ��wC	��A52�A*


Learning Rateo�:s]z�        )�P	�cC52�A*


Loss/traine�@>�gV�       ��2	�cC52�A*

Loss/val\D�=���$       B+�M	�cC52�A*

Accuracy/trainMB�B�'�"       x=�	�cC52�A*

Accuracy/val�M�B��.�#       ��wC	�cC52�A*


Learning Rateo�:6.ϙ        )�P	�RC 52�A*


Loss/train�t�=C���       ��2	�RC 52�A*

Loss/val��=��6�$       B+�M	�RC 52�A*

Accuracy/train`��By�0"       x=�	�RC 52�A*

Accuracy/val6��B&�"�#       ��wC	�RC 52�A*


Learning Rateo�:�k�        )�P	�;"52�A*


Loss/train�G�=�:�       ��2	�;"52�A*

Loss/val�ӎ<C�)-$       B+�M	�;"52�A*

Accuracy/train\�B����"       x=�	�;"52�A*

Accuracy/val#�B}�� #       ��wC	�;"52�A*


Learning Rateo�:SG        )�P	G�?$52�A*


Loss/train y=��       ��2	G�?$52�A*

Loss/val �v<K
�$       B+�M	G�?$52�A*

Accuracy/train  �B��"       x=�	G�?$52�A*

Accuracy/valT�B�Qٌ#       ��wC	G�?$52�A*


Learning Rateo�:f��        )�P	�E&52�A*


Loss/trainWO=�+��       ��2	�E&52�A*

Loss/val�^�<{q�$       B+�M	C"E&52�A*

Accuracy/trainc��B�QN�"       x=�	C"E&52�A*

Accuracy/valMr�B֞e�#       ��wC	C"E&52�A*


Learning Rateo�:�݃        )�P	�$E(52�A*


Loss/train՟C=ڊ       ��2	�$E(52�A*

Loss/valU0M;A.31$       B+�M	�$E(52�A*

Accuracy/train%D�B~���"       x=�	�$E(52�A*

Accuracy/valE��BZ#       ��wC	�$E(52�A*


Learning Rateo�:e�?        )�P	�J*52�A*


Loss/train��=%E�       ��2	^�J*52�A*

Loss/val�Xw;а#�$       B+�M	^�J*52�A*

Accuracy/train���B��_"       x=�	^�J*52�A*

Accuracy/val���B(d=#       ��wC	^�J*52�A*


Learning Rateo�:�!��        )�P	�X[,52�A	*


Loss/train/\0=����       ��2	�X[,52�A	*

Loss/valX=�<�($       B+�M	�X[,52�A	*

Accuracy/train!��Bi��6"       x=�	�X[,52�A	*

Accuracy/valѲ�B��?e#       ��wC	�X[,52�A	*


Learning Rateo�:�n�`        )�P	��f.52�A
*


Loss/train��<���       ��2	��f.52�A
*

Loss/val�|<A�r$       B+�M	��f.52�A
*

Accuracy/trainMB�BM.��"       x=�	��f.52�A
*

Accuracy/val}��Bn��-#       ��wC	��f.52�A
*


Learning Rateo�:v��        )�P	8>q052�A*


Loss/train��=�s��       ��2	8>q052�A*

Loss/val,�;� ��$       B+�M	8>q052�A*

Accuracy/train*��B*O��"       x=�	8>q052�A*

Accuracy/val���B��/#       ��wC	8>q052�A*


Learning Rateo�:�Tc�        )�P	�g�252�A*


Loss/train㺧<,�+p       ��2	�t�252�A*

Loss/val��;�49$       B+�M	�t�252�A*

Accuracy/train9��B��@"       x=�	�t�252�A*

Accuracy/val���B!�wd#       ��wC	�t�252�A*


Learning Rateo�:$Z        )�P	�j�452�A
*


Loss/trainT�=q�A       ��2	�j�452�A
*

Loss/val.�;�T�O$       B+�M	�j�452�A
*

Accuracy/train  �B��D�"       x=�	�j�452�A
*

Accuracy/valE��BU��#       ��wC	�j�452�A
*


Learning Rateo:���Q        )�P	�652�A*


Loss/train��Z<�>��       ��2	�652�A*

Loss/valy�j;���[$       B+�M	�652�A*

Accuracy/trainv@�B	��5"       x=�	�652�A*

Accuracy/val��Bv<�Z#       ��wC	�652�A*


Learning Rateo:$�˥