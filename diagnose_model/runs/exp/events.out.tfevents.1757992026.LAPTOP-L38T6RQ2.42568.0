H       ��H�	n�52�A
brain.Event:2R.
,tensorboard.summary.writer.event_file_writerwF!       ��2	|T@52�A*


Loss/train��?�6�       ���	|T@52�A*

Loss/val5��>u���"       x=�	|T@52�A*

Accuracy/train��B���        )�P	|T@52�A*

Accuracy/valﱸB7ǩ�!       {��	|T@52�A*


Learning Rateo�:p}��        )�P	��A52�A*


Loss/train�/�>]��       ��2	��A52�A*

Loss/val��>d�a$       B+�M	��A52�A*

Accuracy/train���B��"       x=�	��A52�A*

Accuracy/val�M�B^��#       ��wC	��A52�A*


Learning Rateo�:s]z�        )�P	�cC52�A*


Loss/traine�@>�gV�       ��2	�cC52�A*

Loss/val\D�=���$       B+�M	�cC52�A*

Accuracy/trainMB�B�'�"       x=�	�cC52�A*

Accuracy/val�M�B��.�#       ��wC	�cC52�A*


Learning Rateo�:6.ϙ        )�P	�RC 52�A*


Loss/train�t�=C���       ��2	�RC 52�A*

Loss/val��=��6�$       B+�M	�RC 52�A*

Accuracy/train`��By�0"       x=�	�RC 52�A*

Accuracy/val6��B&�"�#       ��wC	�RC 52�A*


Learning Rateo�:�k�        )�P	�;"52�A*


Loss/train�G�=�:�       ��2	�;"52�A*

Loss/val�ӎ<C�)-$       B+�M	�;"52�A*

Accuracy/train\�B����"       x=�	�;"52�A*

Accuracy/val#�B}�� #       ��wC	�;"52�A*


Learning Rateo�:SG        )�P	G�?$52�A*


Loss/train y=��       ��2	G�?$52�A*

Loss/val �v<K
�$       B+�M	G�?$52�A*

Accuracy/train  �B��"       x=�	G�?$52�A*

Accuracy/valT�B�Qٌ#       ��wC	G�?$52�A*


Learning Rateo�:f��        )�P	�E&52�A*


Loss/trainWO=�+��       ��2	�E&52�A*

Loss/val�^�<{q�$       B+�M	C"E&52�A*

Accuracy/trainc��B�QN�"       x=�	C"E&52�A*

Accuracy/valMr�B֞e�#       ��wC	C"E&52�A*


Learning Rateo�:�݃        )�P	�$E(52�A*


Loss/train՟C=ڊ       ��2	�$E(52�A*

Loss/valU0M;A.31$       B+�M	�$E(52�A*

Accuracy/train%D�B~���"       x=�	�$E(52�A*

Accuracy/valE��BZ#       ��wC	�$E(52�A*


Learning Rateo�:e�?        )�P	�J*52�A*


Loss/train��=%E�       ��2	^�J*52�A*

Loss/val�Xw;а#�$       B+�M	^�J*52�A*

Accuracy/train���B��_"       x=�	^�J*52�A*

Accuracy/val���B(d=#       ��wC	^�J*52�A*


Learning Rateo�:�!��        )�P	�X[,52�A	*


Loss/train/\0=����       ��2	�X[,52�A	*

Loss/valX=�<�($       B+�M	�X[,52�A	*

Accuracy/train!��Bi��6"       x=�	�X[,52�A	*

Accuracy/valѲ�B��?e#       ��wC	�X[,52�A	*


Learning Rateo�:�n�`        )�P	��f.52�A
*


Loss/train��<���       ��2	��f.52�A
*

Loss/val�|<A�r$       B+�M	��f.52�A
*

Accuracy/trainMB�BM.��"       x=�	��f.52�A
*

Accuracy/val}��Bn��-#       ��wC	��f.52�A
*


Learning Rateo�:v��        )�P	8>q052�A*


Loss/train��=�s��       ��2	8>q052�A*

Loss/val,�;� ��$       B+�M	8>q052�A*

Accuracy/train*��B*O��"       x=�	8>q052�A*

Accuracy/val���B��/#       ��wC	8>q052�A*


Learning Rateo�:�Tc�        )�P	�g�252�A*


Loss/train㺧<,�+p       ��2	�t�252�A*

Loss/val��;�49$       B+�M	�t�252�A*

Accuracy/train9��B��@"       x=�	�t�252�A*

Accuracy/val���B!�wd#       ��wC	�t�252�A*


Learning Rateo�:$Z        )�P	�j�452�A
*


Loss/trainT�=q�A       ��2	�j�452�A
*

Loss/val.�;�T�O$       B+�M	�j�452�A
*

Accuracy/train  �B��D�"       x=�	�j�452�A
*

Accuracy/valE��BU��#       ��wC	�j�452�A
*


Learning Rateo:���Q        )�P	�652�A*


Loss/train��Z<�>��       ��2	�652�A*

Loss/valy�j;���[$       B+�M	�652�A*

Accuracy/trainv@�B	��5"       x=�	�652�A*

Accuracy/val��Bv<�Z#       ��wC	�652�A*


Learning Rateo:$�˥        )�P	�952�A*


Loss/train�eR<�[]�       ��2	�952�A*

Loss/val��&;����$       B+�M	�952�A*

Accuracy/trainLe�B��rL"       x=�	�952�A*

Accuracy/val���Bho�A#       ��wC	�952�A*


Learning Rateo:�	�        )�P	��;52�A*


Loss/trainFpJ<�:�       ��2	��;52�A*

Loss/val�U;�frV$       B+�M	��;52�A*

Accuracy/train9�B��g="       x=�	��;52�A*

Accuracy/val���B����#       ��wC	��;52�A*


Learning Rateo:���        )�P	���=52�A*


Loss/trainG��;ߺ(6       ��2	���=52�A*

Loss/val�N<K$Z�$       B+�M	���=52�A*

Accuracy/train���B��H""       x=�	���=52�A*

Accuracy/val�1�B�$��#       ��wC	���=52�A*


Learning Rateo:~n��        )�P	�&(@52�A*


Loss/train���;|wG
       ��2	�&(@52�A*

Loss/valr�p8�.w�$       B+�M	�&(@52�A*

Accuracy/train���B8�$"       x=�	�&(@52�A*

Accuracy/val  �B�a>�#       ��wC	7(@52�A*


Learning Rateo:�d?�        )�P	3-RB52�A*


Loss/train�*<��@�       ��2	3-RB52�A*

Loss/valQf�9y�Ga$       B+�M	3-RB52�A*

Accuracy/trainv@�B��q�"       x=�	3-RB52�A*

Accuracy/val  �B�^��#       ��wC	3-RB52�A*


Learning Rateo:��?        )�P	��D52�A*


Loss/traina�<�iy�       ��2	��D52�A*

Loss/vali�08�c7?$       B+�M	��D52�A*

Accuracy/trainݘ�B�fw�"       x=�	��D52�A*

Accuracy/val  �B8S)�#       ��wC	��D52�A*


Learning Rateo:B]&�        )�P	v�G52�A*


Loss/train�<k�i       ��2	v�G52�A*

Loss/val��:�2��$       B+�M	v�G52�A*

Accuracy/trainr��B.�~�"       x=�	v�G52�A*

Accuracy/valE��B���a#       ��wC	v�G52�A*


Learning Rateo:���r        )�P	�QI52�A*


Loss/train���;O'�       ��2	�QI52�A*

Loss/val+�9i���$       B+�M	�QI52�A*

Accuracy/train���B)>M"       x=�	�QI52�A*

Accuracy/val  �B��W`#       ��wC	�QI52�A*


Learning Rateo:1͘C        )�P	
+�K52�A*


Loss/train��$<���a       ��2	
+�K52�A*

Loss/val���:!�$       B+�M	
+�K52�A*

Accuracy/train?Z�BE�"       x=�	
+�K52�A*

Accuracy/valE��B�DA#       ��wC	
+�K52�A*


Learning Rateo:|:�N        )�P	� �M52�A*


Loss/trainߛ<���       ��2	� �M52�A*

Loss/val{�F9����$       B+�M	� �M52�A*

Accuracy/trainXp�B�,�s"       x=�	� �M52�A*

Accuracy/val  �B5��#       ��wC	� �M52�A*


Learning Rateo�9�:jV        )�P	G��O52�A*


Loss/trainhQ;�,�       ��2	G��O52�A*

Loss/val�g�7�c�$       B+�M	G��O52�A*

Accuracy/train{��B���"       x=�	G��O52�A*

Accuracy/val  �B�N�#       ��wC	G��O52�A*


Learning Rateo�9�x1        )�P	�8R52�A*


Loss/train;���       ��2	T�8R52�A*

Loss/val�6�7�:�$       B+�M	T�8R52�A*

Accuracy/trainU��B0�V"       x=�	T�8R52�A*

Accuracy/val  �Bb�K�#       ��wC	T�8R52�A*


Learning Rateo�9LU�9        )�P	5qjT52�A*


Loss/trainG�i;K�       ��2	5qjT52�A*

Loss/val}
9��ͬ$       B+�M	5qjT52�A*

Accuracy/train��B��_�"       x=�	5qjT52�A*

Accuracy/val  �B���#       ��wC	5qjT52�A*


Learning Rateo�97���        )�P	珞V52�A*


Loss/train�m;Nڲ;       ��2	珞V52�A*

Loss/val���8K&�$       B+�M	珞V52�A*

Accuracy/train��BgT��"       x=�	珞V52�A*

Accuracy/val  �BL�y?#       ��wC	珞V52�A*


Learning Rateo�9�݉�        )�P	���X52�A*


Loss/train&�i;gU-
       ��2	���X52�A*

Loss/val���7R��$       B+�M	���X52�A*

Accuracy/train��B���"       x=�	���X52�A*

Accuracy/val  �B��Y#       ��wC	���X52�A*


Learning Rateo�9Vn��        )�P	�)'[52�A*


Loss/traint� ;���       ��2	�)'[52�A*

Loss/val&4�;E�M�$       B+�M	�)'[52�A*

Accuracy/train{��BtD�"       x=�	�)'[52�A*

Accuracy/val*��B�8�>#       ��wC	�)'[52�A*


Learning Rateo9S��w        )�P	t�w]52�A*


Loss/trainkH�;*�#�       ��2	t�w]52�A*

Loss/val(M�8B�$       B+�M	t�w]52�A*

Accuracy/trainH��Bæ��"       x=�	t�w]52�A*

Accuracy/val  �B�2N�#       ��wC	t�w]52�A*


Learning Rateo9egE         )�P	���_52�A *


Loss/train�pg;l[��       ��2	���_52�A *

Loss/val��g9�|�$       B+�M	���_52�A *

Accuracy/train*��B�r��"       x=�	���_52�A *

Accuracy/val  �B��#       ��wC	���_52�A *


Learning Rateo9��O        )�P	|_b52�A!*


Loss/train�e�:F�       ��2	|_b52�A!*

Loss/valh8m���$       B+�M	|_b52�A!*

Accuracy/train7��Bg�;�"       x=�	|_b52�A!*

Accuracy/val  �B��n�#       ��wC	|_b52�A!*


Learning Rateo9\G��        )�P	J=Qd52�A"*


Loss/train��:d���       ��2	J=Qd52�A"*

Loss/val�(s8H�=�$       B+�M	J=Qd52�A"*

Accuracy/train���B^�[h"       x=�	J=Qd52�A"*

Accuracy/val  �B=�#       ��wC	J=Qd52�A"*


Learning Rateo9���        )�P	�Ñf52�A#*


Loss/train�<�:���       ��2	�Ñf52�A#*

Loss/val/}�7)�J"$       B+�M	�Ñf52�A#*

Accuracy/train���B��"       x=�	�Ñf52�A#*

Accuracy/val  �B~a#       ��wC	�Ñf52�A#*


Learning Rateo9�4�1        )�P	h��h52�A$*


Loss/trainh�h;(T�       ��2	h��h52�A$*

Loss/val'I8�E�P$       B+�M	h��h52�A$*

Accuracy/train��B)�l�"       x=�	h��h52�A$*

Accuracy/val  �B��(#       ��wC	h��h52�A$*


Learning Rateo�8ʍ�$        )�P	<k52�A%*


Loss/train�:�YE       ��2	<k52�A%*

Loss/valU4/9���`$       B+�M	<k52�A%*

Accuracy/train7��B��7"       x=�	<k52�A%*

Accuracy/val  �B��Al#       ��wC	<k52�A%*


Learning Rateo�8MǪ�        )�P	��Sm52�A&*


Loss/train�؆:ɯ       ��2	��Sm52�A&*

Loss/val�V8�U$       B+�M	��Sm52�A&*

Accuracy/trainD��B�څ�"       x=�	��Sm52�A&*

Accuracy/val  �B�כ�#       ��wC	��Sm52�A&*


Learning Rateo�8JD�        )�P	�J�o52�A'*


Loss/traink �:a�%       ��2	�J�o52�A'*

Loss/val.�7wz�E$       B+�M	�J�o52�A'*

Accuracy/train���B!�7i"       x=�	�J�o52�A'*

Accuracy/val  �B���#       ��wC	�J�o52�A'*


Learning Rateo�8�}1�        )�P	g��q52�A(*


Loss/train�
;G�n       ��2	g��q52�A(*

Loss/val��8�H�$       B+�M	g��q52�A(*

Accuracy/train���BA1�'"       x=�	g��q52�A(*

Accuracy/val  �BH? 8#       ��wC	g��q52�A(*


Learning Rateo�8"%�        )�P	 t52�A)*


Loss/train��;�S��       ��2	 t52�A)*

Loss/val���7��yJ$       B+�M	 t52�A)*

Accuracy/train���B��R"       x=�	 t52�A)*

Accuracy/val  �B���5#       ��wC	 t52�A)*


Learning Rateo�8��(i        )�P	0eYv52�A**


Loss/train��;G~��       ��2	0eYv52�A**

Loss/valx�8�0=Y$       B+�M	0eYv52�A**

Accuracy/train*��B>1�d"       x=�	0eYv52�A**

Accuracy/val  �B���C#       ��wC	0eYv52�A**


Learning Rateo8fϗ        )�P	�d�x52�A+*


Loss/train���:�ձ�       ��2	�d�x52�A+*

Loss/val1Å7h�F^$       B+�M	�d�x52�A+*

Accuracy/trainD��B�.�7"       x=�	�d�x52�A+*

Accuracy/val  �B�j��#       ��wC	�d�x52�A+*


Learning Rateo8p��        )�P	K��z52�A,*


Loss/train��:%�II       ��2	���z52�A,*

Loss/val�֠9
J�R$       B+�M	���z52�A,*

Accuracy/trainD��Bw�U"       x=�	���z52�A,*

Accuracy/val  �BT��u#       ��wC	���z52�A,*


Learning Rateo8�V�        )�P	[�z}52�A-*


Loss/train��:�]r�       ��2	��z}52�A-*

Loss/val�� :dp��$       B+�M	��z}52�A-*

Accuracy/train*��B��"       x=�	��z}52�A-*

Accuracy/val  �B�1�e#       ��wC	��z}52�A-*


Learning Rateo8m7$�        )�P	�S�52�A.*


Loss/trainU�:*F       ��2	�S�52�A.*

Loss/val2M�8l�R$       B+�M	�S�52�A.*

Accuracy/train���B1��~"       x=�	�S�52�A.*

Accuracy/val  �B���#       ��wC	�S�52�A.*


Learning Rateo8����        )�P	H��52�A/*


Loss/train�*;Du�       ��2	H��52�A/*

Loss/val5��7�|[E$       B+�M	H��52�A/*

Accuracy/train{��B�_DF"       x=�	H��52�A/*

Accuracy/val  �B��_#       ��wC	H��52�A/*


Learning Rateo8'���        )�P	�J�52�A0*


Loss/trainv�:^7Y�       ��2	�J�52�A0*

Loss/val�m�7�tE$       B+�M	�Z�52�A0*

Accuracy/train���B��"       x=�	�Z�52�A0*

Accuracy/val  �B�p#       ��wC	�Z�52�A0*


Learning Rateo�7��1.        )�P	3��52�A1*


Loss/train��:�v�-       ��2	3��52�A1*

Loss/val� �8Y2�j$       B+�M	3��52�A1*

Accuracy/train���B��c"       x=�	3��52�A1*

Accuracy/val  �B��Z#       ��wC	3��52�A1*


Learning Rateo�7CyJ