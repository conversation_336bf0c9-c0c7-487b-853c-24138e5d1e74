# CWRU轴承故障数据加载器使用指南

## 🎯 快速开始

### 基础使用

```python
from diagnose_model.data_loader import get_data_loaders

# 创建数据加载器
train_loader, val_loader, test_loader, class_names, num_classes = get_data_loaders(
    data_path="diagnose_model/data/CRWU",
    batch_size=32,
    segment_length=1024,
    step_size=256,
    sensor_type='DE'
)

print(f"类别数量: {num_classes}")
print(f"类别名称: {class_names}")

# 使用数据加载器
for batch_idx, (data, labels) in enumerate(train_loader):
    print(f"批次 {batch_idx}: 数据形状={data.shape}, 标签形状={labels.shape}")
    # 这里可以进行模型训练
    break
```

### 高级使用（带数据增强）

```python
from diagnose_model.advanced_data_loader import get_advanced_data_loaders

# 创建高级数据加载器
train_loader, val_loader, test_loader, class_names, num_classes = get_advanced_data_loaders(
    data_path="diagnose_model/data/CRWU",
    batch_size=32,
    segment_length=1024,
    step_size=256,
    sensor_type='DE',
    normalize_method='standard',  # 'standard', 'minmax', 'none'
    augmentation=True,           # 启用数据增强
    noise_level=0.01            # 噪声水平
)
```

## 📊 数据集信息

### 支持的故障类型
- **Normal**: 正常状态
- **IR_0007/0014/0021/0028**: 内圈故障（不同深度）
- **OR_Centered/Opposite/Orthogonal**: 外圈故障（不同位置）
- **Ball_0007/0014/0021/0028**: 滚珠故障（不同深度）

### 数据统计
- **总数据段**: 21,721个（step_size=256时）
- **训练集**: 13,900个样本
- **验证集**: 3,476个样本
- **测试集**: 4,345个样本
- **信号长度**: 1024个采样点
- **数据形状**: (batch_size, 1, 1024)

## ⚙️ 参数配置

### 基础参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `data_path` | - | 数据路径（指向CRWU文件夹） |
| `batch_size` | 32 | 批次大小 |
| `segment_length` | 1024 | 信号段长度 |
| `step_size` | 256 | 滑动步长 |
| `sensor_type` | 'DE' | 传感器类型（DE/FE/BA） |
| `test_size` | 0.2 | 测试集比例 |
| `random_state` | 42 | 随机种子 |

### 高级参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `normalize_method` | 'standard' | 标准化方法 |
| `augmentation` | True | 是否使用数据增强 |
| `noise_level` | 0.01 | 噪声水平 |

## 🔧 配置建议

### 1. 信号段长度选择
```python
# 短段 - 快速训练
segment_length=512, step_size=512

# 中段 - 平衡性能（推荐）
segment_length=1024, step_size=256

# 长段 - 更多信息
segment_length=2048, step_size=128
```

### 2. 不同应用场景
```python
# 快速原型开发
batch_size=64, segment_length=512, step_size=512

# 高精度训练
batch_size=32, segment_length=1024, step_size=256, augmentation=True

# 内存受限环境
batch_size=16, segment_length=1024, step_size=512
```

## 🚀 完整训练示例

```python
import torch
import torch.nn as nn
import torch.optim as optim
from diagnose_model.data_loader import get_data_loaders

# 1. 创建数据加载器
train_loader, val_loader, test_loader, class_names, num_classes = get_data_loaders(
    data_path="diagnose_model/data/CRWU",
    batch_size=32,
    segment_length=1024,
    step_size=256
)

# 2. 定义简单的CNN模型
class SimpleCNN(nn.Module):
    def __init__(self, num_classes):
        super(SimpleCNN, self).__init__()
        self.conv1 = nn.Conv1d(1, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, padding=1)
        self.pool = nn.MaxPool1d(2)
        self.fc1 = nn.Linear(64 * 256, 128)
        self.fc2 = nn.Linear(128, num_classes)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.5)
        
    def forward(self, x):
        x = self.pool(self.relu(self.conv1(x)))
        x = self.pool(self.relu(self.conv2(x)))
        x = x.view(x.size(0), -1)
        x = self.dropout(self.relu(self.fc1(x)))
        x = self.fc2(x)
        return x

# 3. 训练设置
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = SimpleCNN(num_classes).to(device)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 4. 训练循环
model.train()
for epoch in range(10):
    running_loss = 0.0
    for batch_idx, (data, labels) in enumerate(train_loader):
        data, labels = data.to(device), labels.to(device)
        
        optimizer.zero_grad()
        outputs = model(data)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()
        
        running_loss += loss.item()
        
        if batch_idx % 100 == 0:
            print(f'Epoch {epoch}, Batch {batch_idx}, Loss: {loss.item():.4f}')
    
    print(f'Epoch {epoch} 平均损失: {running_loss/len(train_loader):.4f}')
```

## 🔍 故障排除

### 常见问题

1. **内存不足**
   ```python
   # 解决方案：减小批次大小或信号长度
   batch_size=16, segment_length=512
   ```

2. **数据加载慢**
   ```python
   # 解决方案：增大步长，减少数据重叠
   step_size=512  # 或 step_size=segment_length
   ```

3. **类别不平衡**
   ```python
   # 解决方案：使用数据增强
   from diagnose_model.advanced_data_loader import get_advanced_data_loaders
   # augmentation=True, noise_level=0.02
   ```

### 验证数据加载
```python
# 运行测试脚本
python diagnose_model/test_data_loader.py

# 或者简单验证
from diagnose_model.data_loader import get_data_loaders
train_loader, _, _, class_names, num_classes = get_data_loaders("diagnose_model/data/CRWU")
print(f"成功加载 {num_classes} 个类别: {class_names}")
```

## 📈 性能优化

### 1. 数据加载优化
```python
# 使用更多工作进程（如果内存允许）
DataLoader(dataset, batch_size=32, num_workers=4, pin_memory=True)
```

### 2. 内存优化
```python
# 使用较大的步长减少数据重叠
step_size = segment_length  # 无重叠

# 或者使用较小的批次大小
batch_size = 16
```

### 3. 训练优化
```python
# 使用混合精度训练（如果支持）
from torch.cuda.amp import autocast, GradScaler

scaler = GradScaler()
with autocast():
    outputs = model(data)
    loss = criterion(outputs, labels)
```

## 📝 注意事项

1. **数据路径**: 确保数据路径指向包含CRWU文件夹的目录
2. **传感器选择**: DE传感器通常效果最好，FE传感器也可用
3. **随机种子**: 使用固定的random_state确保结果可重现
4. **数据增强**: 只在训练集使用，验证集和测试集不使用

现在您的CWRU轴承故障数据加载器已经完全可以使用了！🎉
