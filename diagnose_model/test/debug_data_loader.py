"""
调试版本的数据加载器，用于排查问题
"""

import numpy as np
import scipy.io
import os
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import Dataset, DataLoader
import warnings
warnings.filterwarnings('ignore')


def debug_load_single_file():
    """调试单个文件加载"""
    print("=== 调试单个文件加载 ===")
    
    # 测试加载一个正常文件
    normal_file = "../data/CRWU/Normal Baseline/normal_0.mat"
    
    if not os.path.exists(normal_file):
        print(f"文件不存在: {normal_file}")
        return None
        
    try:
        mat_data = scipy.io.loadmat(normal_file)
        print(f"MAT文件键值: {list(mat_data.keys())}")
        
        # 找到信号键
        signal_keys = [k for k in mat_data.keys() if not k.startswith('__') and 'time' in k]
        print(f"信号键: {signal_keys}")
        
        if signal_keys:
            signal_key = signal_keys[0]  # 取第一个
            signal = mat_data[signal_key].flatten()
            print(f"信号形状: {signal.shape}")
            print(f"信号统计: 均值={np.mean(signal):.4f}, 标准差={np.std(signal):.4f}")
            
            # 测试分割
            segment_length = 1024
            step_size = 256
            segments = []
            
            for i in range(0, len(signal) - segment_length + 1, step_size):
                segment = signal[i:i + segment_length]
                segments.append(segment)
                
            print(f"分割后段数: {len(segments)}")
            if segments:
                print(f"每段形状: {np.array(segments[0]).shape}")
                
            return np.array(segments)
        
    except Exception as e:
        print(f"加载文件时出错: {e}")
        import traceback
        traceback.print_exc()
        
    return None


def debug_data_shape():
    """调试数据形状问题"""
    print("\n=== 调试数据形状 ===")
    
    # 模拟数据加载过程
    data = []
    labels = []
    
    # 添加一些测试数据
    for i in range(10):
        # 创建1024长度的随机信号
        signal = np.random.randn(1024)
        data.append(signal)
        labels.append('Normal')
        
    print(f"数据列表长度: {len(data)}")
    print(f"第一个信号形状: {np.array(data[0]).shape}")
    
    # 转换为numpy数组
    data_array = np.array(data)
    labels_array = np.array(labels)
    
    print(f"数据数组形状: {data_array.shape}")
    print(f"标签数组形状: {labels_array.shape}")
    
    # 测试StandardScaler
    try:
        scaler = StandardScaler()
        data_scaled = scaler.fit_transform(data_array)
        print(f"标准化后形状: {data_scaled.shape}")
        print("StandardScaler测试成功!")
    except Exception as e:
        print(f"StandardScaler测试失败: {e}")
        
        # 尝试重塑数据
        if len(data_array.shape) == 1:
            data_reshaped = data_array.reshape(-1, 1)
        elif len(data_array.shape) > 2:
            data_reshaped = data_array.reshape(data_array.shape[0], -1)
        else:
            data_reshaped = data_array
            
        print(f"重塑后形状: {data_reshaped.shape}")
        
        try:
            data_scaled = scaler.fit_transform(data_reshaped)
            print(f"重塑后标准化成功: {data_scaled.shape}")
        except Exception as e2:
            print(f"重塑后仍然失败: {e2}")
    
    return data_array, labels_array


def debug_full_loading():
    """调试完整加载过程"""
    print("\n=== 调试完整加载过程 ===")
    
    data_path = "../data/CRWU"
    
    if not os.path.exists(data_path):
        print(f"数据路径不存在: {data_path}")
        return
        
    data = []
    labels = []
    
    # 1. 加载正常数据
    normal_path = os.path.join(data_path, "Normal Baseline")
    if os.path.exists(normal_path):
        print(f"正常数据路径: {normal_path}")
        normal_files = [f for f in os.listdir(normal_path) if f.endswith('.mat')]
        print(f"正常文件: {normal_files[:3]}...")  # 只显示前3个
        
        # 只加载第一个文件进行测试
        if normal_files:
            file_path = os.path.join(normal_path, normal_files[0])
            try:
                mat_data = scipy.io.loadmat(file_path)
                
                # 找到DE信号
                signal_key = None
                for key in mat_data.keys():
                    if not key.startswith('__') and '_DE_time' in key:
                        signal_key = key
                        break
                
                if signal_key:
                    signal = mat_data[signal_key].flatten()
                    print(f"信号长度: {len(signal)}")
                    
                    # 分割信号
                    segment_length = 1024
                    step_size = 256
                    
                    for i in range(0, min(len(signal) - segment_length + 1, step_size * 5), step_size):
                        segment = signal[i:i + segment_length]
                        data.append(segment)
                        labels.append('Normal')
                        
                    print(f"添加了 {len(data)} 个正常段")
                    
            except Exception as e:
                print(f"加载正常文件时出错: {e}")
    
    # 2. 加载一些故障数据
    fault_path = os.path.join(data_path, "12k Drive End Bearing Fault Data", "Inner Race", "0007")
    if os.path.exists(fault_path):
        print(f"故障数据路径: {fault_path}")
        fault_files = [f for f in os.listdir(fault_path) if f.endswith('.mat')]
        
        if fault_files:
            file_path = os.path.join(fault_path, fault_files[0])
            try:
                mat_data = scipy.io.loadmat(file_path)
                
                # 找到DE信号
                signal_key = None
                for key in mat_data.keys():
                    if not key.startswith('__') and '_DE_time' in key:
                        signal_key = key
                        break
                
                if signal_key:
                    signal = mat_data[signal_key].flatten()
                    
                    # 分割信号
                    for i in range(0, min(len(signal) - segment_length + 1, step_size * 5), step_size):
                        segment = signal[i:i + segment_length]
                        data.append(segment)
                        labels.append('IR_0007')
                        
                    print(f"总共有 {len(data)} 个数据段")
                    
            except Exception as e:
                print(f"加载故障文件时出错: {e}")
    
    if data:
        print(f"数据列表长度: {len(data)}")
        print(f"标签列表长度: {len(labels)}")
        
        # 转换为numpy数组
        try:
            data_array = np.array(data)
            labels_array = np.array(labels)
            
            print(f"数据数组形状: {data_array.shape}")
            print(f"标签数组形状: {labels_array.shape}")
            
            # 测试标准化
            if len(data_array.shape) == 2:
                scaler = StandardScaler()
                data_scaled = scaler.fit_transform(data_array)
                print(f"标准化成功: {data_scaled.shape}")
                
                # 测试标签编码
                label_encoder = LabelEncoder()
                labels_encoded = label_encoder.fit_transform(labels_array)
                print(f"标签编码成功: {labels_encoded.shape}")
                print(f"唯一标签: {np.unique(labels_encoded)}")
                
                return data_scaled, labels_encoded
            else:
                print(f"数据形状不正确: {data_array.shape}")
                
        except Exception as e:
            print(f"数组转换失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("没有加载到任何数据!")
        
    return None, None


def main():
    """主调试函数"""
    print("开始调试数据加载器...")
    
    # 1. 调试单个文件
    segments = debug_load_single_file()
    
    # 2. 调试数据形状
    test_data, test_labels = debug_data_shape()
    
    # 3. 调试完整加载
    real_data, real_labels = debug_full_loading()
    
    print("\n=== 调试完成 ===")
    
    if real_data is not None:
        print("数据加载成功!")
        print(f"最终数据形状: {real_data.shape}")
        print(f"最终标签形状: {real_labels.shape}")
    else:
        print("数据加载失败，请检查上述错误信息")


if __name__ == "__main__":
    main()
