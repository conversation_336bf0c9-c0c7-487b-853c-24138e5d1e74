"""
简单的数据加载器测试脚本
"""

import os
import sys
import numpy as np
import traceback

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_loading():
    """测试基础数据加载"""
    print("=" * 50)
    print("测试基础数据加载")
    print("=" * 50)
    
    try:
        from diagnose_model.data_loader import CWRUBearingDataset
        
        data_path = "../data/CRWU"
        
        # 检查数据路径
        if not os.path.exists(data_path):
            print(f"错误: 数据路径不存在: {data_path}")
            return False
            
        print(f"数据路径存在: {data_path}")
        
        # 创建数据集
        dataset = CWRUBearingDataset(
            data_path=data_path,
            segment_length=1024,
            step_size=512,  # 使用较大的步长减少数据量
            mode='train',
            sensor_type='DE',
            normalize=True,
            test_size=0.2,
            random_state=42
        )
        
        print(f"数据集创建成功!")
        print(f"数据集大小: {len(dataset)}")
        print(f"类别数量: {dataset.get_num_classes()}")
        print(f"类别名称: {dataset.get_class_names()}")
        
        # 测试获取一个样本
        if len(dataset) > 0:
            sample_data, sample_label = dataset[0]
            print(f"样本数据形状: {sample_data.shape}")
            print(f"样本标签: {sample_label}")
            print("基础数据加载测试成功!")
            return True
        else:
            print("错误: 数据集为空")
            return False
            
    except Exception as e:
        print(f"基础数据加载测试失败: {e}")
        traceback.print_exc()
        return False


def test_data_loader_function():
    """测试数据加载器函数"""
    print("\n" + "=" * 50)
    print("测试数据加载器函数")
    print("=" * 50)
    
    try:
        from diagnose_model.data_loader import get_data_loaders
        
        data_path = "../data/CRWU"
        
        train_loader, val_loader, test_loader, class_names, num_classes = get_data_loaders(
            data_path=data_path,
            batch_size=16,  # 使用较小的批次大小
            segment_length=1024,
            step_size=512,
            sensor_type='DE',
            normalize=True,
            test_size=0.2,
            random_state=42
        )
        
        print(f"数据加载器创建成功!")
        print(f"训练集批次数: {len(train_loader)}")
        print(f"验证集批次数: {len(val_loader)}")
        print(f"测试集批次数: {len(test_loader)}")
        print(f"类别数量: {num_classes}")
        print(f"类别名称: {class_names}")
        
        # 测试获取一个批次
        for i, (data, labels) in enumerate(train_loader):
            print(f"第一个批次数据形状: {data.shape}")
            print(f"第一个批次标签形状: {labels.shape}")
            print(f"标签范围: {labels.min().item()} - {labels.max().item()}")
            break
            
        print("数据加载器函数测试成功!")
        return True
        
    except Exception as e:
        print(f"数据加载器函数测试失败: {e}")
        traceback.print_exc()
        return False


def test_different_sensors():
    """测试不同传感器"""
    print("\n" + "=" * 50)
    print("测试不同传感器")
    print("=" * 50)
    
    sensors = ['DE', 'FE']
    results = {}
    
    for sensor in sensors:
        print(f"\n测试 {sensor} 传感器...")
        try:
            from data_loader import CWRUBearingDataset
            
            dataset = CWRUBearingDataset(
                data_path="../data/CRWU",
                segment_length=1024,
                step_size=512,
                mode='train',
                sensor_type=sensor,
                normalize=True,
                test_size=0.2,
                random_state=42
            )
            
            results[sensor] = {
                'success': True,
                'size': len(dataset),
                'classes': dataset.get_num_classes()
            }
            
            print(f"{sensor} 传感器测试成功: {len(dataset)} 样本, {dataset.get_num_classes()} 类别")
            
        except Exception as e:
            results[sensor] = {
                'success': False,
                'error': str(e)
            }
            print(f"{sensor} 传感器测试失败: {e}")
    
    return results


def test_path_validation():
    """测试路径验证"""
    print("\n" + "=" * 50)
    print("测试路径验证")
    print("=" * 50)
    
    # 测试错误路径
    try:
        from data_loader import CWRUBearingDataset
        
        dataset = CWRUBearingDataset(
            data_path="non_existent_path",
            segment_length=1024,
            step_size=512,
            mode='train',
            sensor_type='DE',
            normalize=True
        )
        
        print("错误: 应该抛出异常但没有")
        return False
        
    except Exception as e:
        print(f"正确捕获了路径错误: {e}")
        return True


def main():
    """主测试函数"""
    print("开始数据加载器测试...")
    
    results = []
    
    # 1. 测试基础数据加载
    results.append(("基础数据加载", test_basic_loading()))
    
    # 2. 测试数据加载器函数
    results.append(("数据加载器函数", test_data_loader_function()))
    
    # 3. 测试不同传感器
    sensor_results = test_different_sensors()
    for sensor, result in sensor_results.items():
        results.append((f"{sensor}传感器", result['success']))
    
    # 4. 测试路径验证
    results.append(("路径验证", test_path_validation()))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了!")
        return True
    else:
        print("⚠️  有测试失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
