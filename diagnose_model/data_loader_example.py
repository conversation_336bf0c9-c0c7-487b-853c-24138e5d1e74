"""
CWRU轴承数据集加载器使用示例

这个文件展示了如何使用不同的数据加载器来加载和处理CWRU轴承数据集
"""

import numpy as np
import torch
import matplotlib.pyplot as plt
from data_loader import get_data_loaders, analyze_dataset
from advanced_data_loader import get_advanced_data_loaders
from data_visualization import DataVisualizer
import time


def basic_data_loader_example():
    """基础数据加载器示例"""
    print("=" * 50)
    print("基础数据加载器示例")
    print("=" * 50)
    
    data_path = "diagnose_model/data/CRWU"
    
    # 创建数据加载器
    train_loader, val_loader, test_loader, class_names, num_classes = get_data_loaders(
        data_path=data_path,
        batch_size=32,
        segment_length=1024,
        step_size=256,
        sensor_type='DE',
        normalize=True,
        test_size=0.2,
        random_state=42
    )
    
    print(f"\n数据集信息:")
    print(f"类别数量: {num_classes}")
    print(f"类别名称: {class_names}")
    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")
    
    # 测试数据加载
    print(f"\n测试数据加载:")
    for i, (data, labels) in enumerate(train_loader):
        print(f"批次 {i+1}: 数据形状={data.shape}, 标签形状={labels.shape}")
        if i >= 2:  # 只显示前3个批次
            break
    
    return train_loader, val_loader, test_loader, class_names, num_classes


def advanced_data_loader_example():
    """高级数据加载器示例"""
    print("\n" + "=" * 50)
    print("高级数据加载器示例 (带数据增强)")
    print("=" * 50)
    
    data_path = "diagnose_model/data/CRWU"
    
    # 创建高级数据加载器
    train_loader, val_loader, test_loader, class_names, num_classes = get_advanced_data_loaders(
        data_path=data_path,
        batch_size=32,
        segment_length=1024,
        step_size=256,
        sensor_type='DE',
        normalize_method='standard',  # 'standard', 'minmax', 'none'
        augmentation=True,
        noise_level=0.01,
        test_size=0.2,
        random_state=42
    )
    
    print(f"\n高级数据集信息:")
    print(f"类别数量: {num_classes}")
    print(f"类别名称: {class_names}")
    
    # 比较增强前后的数据
    print(f"\n数据增强效果对比:")
    
    # 获取一个样本
    data_iter = iter(train_loader)
    batch_data, batch_labels = next(data_iter)
    
    # 显示第一个样本
    sample_signal = batch_data[0, 0, :].numpy()  # 取第一个样本的信号
    sample_label = batch_labels[0].item()
    
    print(f"样本标签: {class_names[sample_label]}")
    print(f"信号统计: 均值={np.mean(sample_signal):.4f}, 标准差={np.std(sample_signal):.4f}")
    
    return train_loader, val_loader, test_loader, class_names, num_classes


def compare_different_sensors():
    """比较不同传感器的数据"""
    print("\n" + "=" * 50)
    print("不同传感器数据对比")
    print("=" * 50)
    
    data_path = "diagnose_model/data/CRWU"
    sensors = ['DE', 'FE']  # 驱动端和风扇端
    
    for sensor in sensors:
        print(f"\n--- {sensor} 传感器 ---")
        try:
            train_loader, _, _, class_names, num_classes = get_data_loaders(
                data_path=data_path,
                batch_size=32,
                segment_length=1024,
                step_size=512,
                sensor_type=sensor,
                test_size=0.2
            )
            
            print(f"成功加载 {sensor} 传感器数据")
            print(f"类别数量: {num_classes}")
            
            # 获取一个批次的数据进行分析
            data_iter = iter(train_loader)
            batch_data, _ = next(data_iter)
            
            print(f"数据统计: 均值={torch.mean(batch_data):.4f}, 标准差={torch.std(batch_data):.4f}")
            
        except Exception as e:
            print(f"加载 {sensor} 传感器数据时出错: {e}")


def data_analysis_example():
    """数据分析示例"""
    print("\n" + "=" * 50)
    print("数据分析示例")
    print("=" * 50)
    
    data_path = "diagnose_model/data/CRWU"
    
    # 分析数据集
    dataset = analyze_dataset(data_path, sensor_type='DE')
    
    return dataset


def visualization_example():
    """数据可视化示例"""
    print("\n" + "=" * 50)
    print("数据可视化示例")
    print("=" * 50)
    
    data_path = "diagnose_model/data/CRWU"
    
    # 创建可视化工具
    visualizer = DataVisualizer(data_path, sensor_type='DE')
    
    # 生成部分可视化图表
    print("生成信号样本图...")
    visualizer.plot_signal_samples(num_samples=2)
    
    print("生成类别分布图...")
    visualizer.plot_class_distribution()
    
    return visualizer


def performance_test():
    """性能测试"""
    print("\n" + "=" * 50)
    print("数据加载性能测试")
    print("=" * 50)
    
    data_path = "diagnose_model/data/CRWU"
    
    # 测试不同批次大小的加载速度
    batch_sizes = [16, 32, 64, 128]
    
    for batch_size in batch_sizes:
        print(f"\n测试批次大小: {batch_size}")
        
        start_time = time.time()
        
        train_loader, _, _, _, _ = get_data_loaders(
            data_path=data_path,
            batch_size=batch_size,
            segment_length=1024,
            step_size=256,
            sensor_type='DE'
        )
        
        # 测试加载10个批次的时间
        load_time = 0
        for i, (data, labels) in enumerate(train_loader):
            if i >= 10:
                break
            batch_start = time.time()
            # 模拟一些处理
            _ = torch.mean(data)
            load_time += time.time() - batch_start
        
        total_time = time.time() - start_time
        
        print(f"  总时间: {total_time:.2f}s")
        print(f"  平均每批次: {load_time/min(10, len(train_loader)):.4f}s")


def custom_configuration_example():
    """自定义配置示例"""
    print("\n" + "=" * 50)
    print("自定义配置示例")
    print("=" * 50)
    
    data_path = "diagnose_model/data/CRWU"
    
    # 配置1: 长信号段，小步长 (更多重叠)
    print("配置1: 长信号段，小步长")
    train_loader1, _, _, _, _ = get_data_loaders(
        data_path=data_path,
        batch_size=32,
        segment_length=2048,  # 更长的信号段
        step_size=128,        # 更小的步长
        sensor_type='DE'
    )
    
    # 配置2: 短信号段，大步长 (更少重叠)
    print("\n配置2: 短信号段，大步长")
    train_loader2, _, _, _, _ = get_data_loaders(
        data_path=data_path,
        batch_size=32,
        segment_length=512,   # 更短的信号段
        step_size=512,        # 更大的步长
        sensor_type='DE'
    )
    
    # 配置3: 数据增强配置
    print("\n配置3: 强数据增强")
    train_loader3, _, _, _, _ = get_advanced_data_loaders(
        data_path=data_path,
        batch_size=32,
        segment_length=1024,
        step_size=256,
        sensor_type='DE',
        normalize_method='minmax',  # 使用MinMax标准化
        augmentation=True,
        noise_level=0.05,          # 更高的噪声水平
    )
    
    print("所有配置测试完成!")


def main():
    """主函数 - 运行所有示例"""
    print("CWRU轴承数据集加载器完整示例")
    print("=" * 60)
    
    try:
        # 1. 基础数据加载器
        basic_data_loader_example()
        
        # 2. 高级数据加载器
        advanced_data_loader_example()
        
        # 3. 不同传感器对比
        compare_different_sensors()
        
        # 4. 数据分析
        data_analysis_example()
        
        # 5. 数据可视化 (注释掉以避免弹出窗口)
        # visualization_example()
        
        # 6. 性能测试
        performance_test()
        
        # 7. 自定义配置
        custom_configuration_example()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成!")
        print("=" * 60)
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
