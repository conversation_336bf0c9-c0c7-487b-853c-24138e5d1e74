import numpy as np
import scipy.io
import os
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.model_selection import train_test_split, StratifiedKFold
import torch
from torch.utils.data import Dataset, DataLoader
import warnings
import matplotlib.pyplot as plt
from scipy import signal as scipy_signal
import random

warnings.filterwarnings('ignore')


class AdvancedCWRUBearingDataset(Dataset):
    def __init__(self, data_path, segment_length=1024, step_size=256, mode='train', 
                 test_size=0.2, sensor_type='DE', normalize_method='standard',
                 augmentation=False, noise_level=0.01, random_state=42):
        """
        高级CWRU轴承数据集加载器，支持数据增强和多种预处理方法
        
        :param data_path: 数据目录路径
        :param segment_length: 信号段长度
        :param step_size: 滑动步长
        :param mode: 'train', 'val' 或 'test'
        :param test_size: 测试集比例
        :param sensor_type: 传感器类型 ('DE', 'FE', 'BA')
        :param normalize_method: 标准化方法 ('standard', 'minmax', 'none')
        :param augmentation: 是否使用数据增强
        :param noise_level: 噪声水平 (用于数据增强)
        :param random_state: 随机种子
        """
        self.segment_length = segment_length
        self.step_size = step_size
        self.mode = mode
        self.sensor_type = sensor_type
        self.normalize_method = normalize_method
        self.augmentation = augmentation and mode == 'train'  # 只在训练时使用增强
        self.noise_level = noise_level
        self.random_state = random_state
        
        # 设置随机种子
        np.random.seed(random_state)
        random.seed(random_state)

        # 加载并处理数据
        data, labels, self.class_names = self.load_cwru_data(data_path)
        
        print(f"原始数据形状: {data.shape}")
        print(f"标签分布: {np.unique(labels, return_counts=True)}")

        # 确保数据是2D格式
        if len(data.shape) == 1:
            data = data.reshape(-1, 1)
        elif len(data.shape) > 2:
            data = data.reshape(data.shape[0], -1)

        print(f"重塑后数据形状: {data.shape}")

        # 数据标准化
        self.scaler = self._get_scaler(normalize_method)
        if self.scaler:
            data = self.scaler.fit_transform(data)

        # 编码标签
        self.label_encoder = LabelEncoder()
        labels_encoded = self.label_encoder.fit_transform(labels)
        
        # 创建标签映射
        self.label_mapping = dict(zip(self.label_encoder.classes_, 
                                    range(len(self.label_encoder.classes_))))
        print(f"标签映射: {self.label_mapping}")

        # 检查每个类别的样本数量
        unique_labels, label_counts = np.unique(labels_encoded, return_counts=True)
        min_samples = np.min(label_counts)
        print(f"最少样本数的类别有 {min_samples} 个样本")

        # 如果样本数太少，不使用分层抽样
        use_stratify = min_samples >= 2  # 至少需要2个样本才能分层

        # 分割数据集
        if use_stratify:
            X_train, X_test, y_train, y_test = train_test_split(
                data, labels_encoded, test_size=test_size, random_state=random_state,
                stratify=labels_encoded
            )
        else:
            print("样本数太少，不使用分层抽样")
            X_train, X_test, y_train, y_test = train_test_split(
                data, labels_encoded, test_size=test_size, random_state=random_state
            )

        # 检查训练集中每个类别的样本数
        unique_train_labels, train_label_counts = np.unique(y_train, return_counts=True)
        min_train_samples = np.min(train_label_counts)
        use_stratify_val = min_train_samples >= 2

        if use_stratify_val:
            X_train, X_val, y_train, y_val = train_test_split(
                X_train, y_train, test_size=0.2, random_state=random_state,
                stratify=y_train
            )
        else:
            print("训练集样本数太少，验证集分割不使用分层抽样")
            X_train, X_val, y_train, y_val = train_test_split(
                X_train, y_train, test_size=0.2, random_state=random_state
            )

        # 根据模式选择数据
        if mode == 'train':
            self.data = X_train
            self.labels = y_train
        elif mode == 'val':
            self.data = X_val
            self.labels = y_val
        else:  # test
            self.data = X_test
            self.labels = y_test

        print(f"加载了 {len(self.data)} 个样本用于 {mode} 集")
        print(f"数据形状: {self.data.shape}")
        print(f"标签形状: {self.labels.shape}")
        print(f"类别数量: {len(np.unique(self.labels))}")

    def _get_scaler(self, method):
        """获取标准化器"""
        if method == 'standard':
            return StandardScaler()
        elif method == 'minmax':
            return MinMaxScaler()
        else:
            return None

    def load_cwru_data(self, data_path):
        """加载CWRU数据集"""
        data = []
        labels = []
        class_names = []

        print(f"从路径加载数据: {data_path}")

        # 1. 加载正常数据
        normal_path = os.path.join(data_path, "Normal Baseline")
        if os.path.exists(normal_path):
            print("加载正常数据...")
            normal_files = [f for f in os.listdir(normal_path) if f.endswith('.mat')]
            
            for file in normal_files:
                file_path = os.path.join(normal_path, file)
                try:
                    mat_data = scipy.io.loadmat(file_path)
                    signal_key = self._get_signal_key(mat_data, self.sensor_type)
                    if signal_key:
                        signal = mat_data[signal_key].flatten()
                        segments, segment_labels = self._segment_signal(signal, 'Normal')
                        data.extend(segments)
                        labels.extend(segment_labels)
                        
                except Exception as e:
                    print(f"加载文件 {file} 时出错: {e}")
                    continue
            
            if 'Normal' not in class_names:
                class_names.append('Normal')

        # 2. 加载故障数据
        fault_base_path = os.path.join(data_path, "12k Drive End Bearing Fault Data")
        if os.path.exists(fault_base_path):
            print("加载故障数据...")
            
            # 内圈故障
            self._load_fault_data(fault_base_path, "Inner Race", "IR", data, labels, class_names)
            
            # 外圈故障
            self._load_fault_data(fault_base_path, "Outer Race", "OR", data, labels, class_names)
            
            # 滚珠故障
            self._load_fault_data(fault_base_path, "Ball", "Ball", data, labels, class_names)

        print(f"总共加载了 {len(data)} 个数据段")
        print(f"类别: {class_names}")
        
        return np.array(data), np.array(labels), class_names

    def _get_signal_key(self, mat_data, sensor_type):
        """根据传感器类型获取信号键名"""
        keys = [k for k in mat_data.keys() if not k.startswith('__')]
        
        for key in keys:
            if sensor_type == 'DE' and '_DE_time' in key:
                return key
            elif sensor_type == 'FE' and '_FE_time' in key:
                return key
            elif sensor_type == 'BA' and '_BA_time' in key:
                return key
        
        # 如果没找到指定类型，返回第一个时间序列数据
        for key in keys:
            if 'time' in key and not 'RPM' in key:
                return key
        
        return None

    def _segment_signal(self, signal, label):
        """将信号分割成固定长度的段"""
        segments = []
        segment_labels = []
        
        for i in range(0, len(signal) - self.segment_length + 1, self.step_size):
            segment = signal[i:i + self.segment_length]
            segments.append(segment)
            segment_labels.append(label)
            
        return segments, segment_labels

    def _load_fault_data(self, base_path, fault_type_folder, fault_prefix, data, labels, class_names):
        """加载特定类型的故障数据"""
        fault_path = os.path.join(base_path, fault_type_folder)
        
        if not os.path.exists(fault_path):
            print(f"路径不存在: {fault_path}")
            return
            
        # 遍历故障深度文件夹
        for depth_folder in os.listdir(fault_path):
            depth_path = os.path.join(fault_path, depth_folder)
            
            if os.path.isdir(depth_path):
                print(f"加载 {fault_type_folder} - {depth_folder} 数据...")
                
                # 创建标签
                label = f"{fault_prefix}_{depth_folder}"
                
                if label not in class_names:
                    class_names.append(label)
                
                # 加载该深度下的所有文件
                mat_files = [f for f in os.listdir(depth_path) if f.endswith('.mat')]
                
                for file in mat_files:
                    file_path = os.path.join(depth_path, file)
                    try:
                        mat_data = scipy.io.loadmat(file_path)
                        signal_key = self._get_signal_key(mat_data, self.sensor_type)
                        
                        if signal_key:
                            signal = mat_data[signal_key].flatten()
                            segments, segment_labels = self._segment_signal(signal, label)
                            data.extend(segments)
                            labels.extend(segment_labels)
                            
                    except Exception as e:
                        print(f"加载文件 {file} 时出错: {e}")
                        continue

    def _add_noise(self, signal, noise_level):
        """添加高斯噪声"""
        noise = np.random.normal(0, noise_level * np.std(signal), signal.shape)
        return signal + noise

    def _time_shift(self, signal, max_shift=50):
        """时间偏移"""
        shift = np.random.randint(-max_shift, max_shift)
        if shift > 0:
            return np.concatenate([signal[shift:], signal[:shift]])
        elif shift < 0:
            return np.concatenate([signal[shift:], signal[:shift]])
        else:
            return signal

    def _amplitude_scale(self, signal, scale_range=(0.8, 1.2)):
        """幅度缩放"""
        scale = np.random.uniform(scale_range[0], scale_range[1])
        return signal * scale

    def _apply_augmentation(self, signal):
        """应用数据增强"""
        if not self.augmentation:
            return signal
            
        # 随机选择增强方法
        aug_methods = []
        
        if np.random.random() < 0.3:  # 30% 概率添加噪声
            aug_methods.append('noise')
        if np.random.random() < 0.3:  # 30% 概率时间偏移
            aug_methods.append('shift')
        if np.random.random() < 0.3:  # 30% 概率幅度缩放
            aug_methods.append('scale')
        
        for method in aug_methods:
            if method == 'noise':
                signal = self._add_noise(signal, self.noise_level)
            elif method == 'shift':
                signal = self._time_shift(signal)
            elif method == 'scale':
                signal = self._amplitude_scale(signal)
                
        return signal

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        # 获取信号和标签
        signal = self.data[idx].copy()
        label = self.labels[idx]
        
        # 应用数据增强
        signal = self._apply_augmentation(signal)
        
        # 转换为适合CNN输入的格式 (通道, 长度)
        signal = signal.reshape(1, -1)
        
        return torch.FloatTensor(signal), torch.tensor(label, dtype=torch.long)
    
    def get_class_names(self):
        """获取类别名称"""
        return self.class_names
    
    def get_label_mapping(self):
        """获取标签映射"""
        return self.label_mapping
    
    def get_num_classes(self):
        """获取类别数量"""
        return len(self.class_names)


def get_advanced_data_loaders(data_path, batch_size=32, segment_length=1024, step_size=256, 
                            sensor_type='DE', normalize_method='standard', 
                            augmentation=True, noise_level=0.01,
                            test_size=0.2, random_state=42):
    """
    创建高级数据加载器
    
    :param data_path: 数据路径
    :param batch_size: 批次大小
    :param segment_length: 信号段长度
    :param step_size: 滑动步长
    :param sensor_type: 传感器类型
    :param normalize_method: 标准化方法
    :param augmentation: 是否使用数据增强
    :param noise_level: 噪声水平
    :param test_size: 测试集比例
    :param random_state: 随机种子
    :return: train_loader, val_loader, test_loader, class_names, num_classes
    """
    print("创建高级数据加载器...")
    
    # 创建数据集
    train_dataset = AdvancedCWRUBearingDataset(
        data_path, segment_length, step_size, mode='train',
        test_size=test_size, sensor_type=sensor_type, 
        normalize_method=normalize_method, augmentation=augmentation,
        noise_level=noise_level, random_state=random_state
    )
    
    val_dataset = AdvancedCWRUBearingDataset(
        data_path, segment_length, step_size, mode='val',
        test_size=test_size, sensor_type=sensor_type, 
        normalize_method=normalize_method, augmentation=False,  # 验证集不使用增强
        noise_level=noise_level, random_state=random_state
    )
    
    test_dataset = AdvancedCWRUBearingDataset(
        data_path, segment_length, step_size, mode='test',
        test_size=test_size, sensor_type=sensor_type, 
        normalize_method=normalize_method, augmentation=False,  # 测试集不使用增强
        noise_level=noise_level, random_state=random_state
    )

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True, 
        num_workers=0, pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False, 
        num_workers=0, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False, 
        num_workers=0, pin_memory=True
    )

    # 获取类别信息
    class_names = train_dataset.get_class_names()
    num_classes = train_dataset.get_num_classes()
    
    print(f"高级数据加载器创建完成:")
    print(f"  训练集: {len(train_dataset)} 样本 (增强: {augmentation})")
    print(f"  验证集: {len(val_dataset)} 样本")
    print(f"  测试集: {len(test_dataset)} 样本")
    print(f"  类别数: {num_classes}")
    print(f"  标准化方法: {normalize_method}")

    return train_loader, val_loader, test_loader, class_names, num_classes
