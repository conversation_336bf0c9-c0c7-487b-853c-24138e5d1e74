import numpy as np
import pandas as pd
import scipy.io
import os
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import Dataset, DataLoader


class CWRUBearingDataset(Dataset):
    def __init__(self, data_path, segment_length=1024, step_size=256, mode='train', test_size=0.2):
        """
        加载CWRU轴承数据集
        :param data_path: 数据目录路径
        :param segment_length: 信号段长度
        :param step_size: 滑动步长
        :param mode: 'train', 'val' 或 'test'
        :param test_size: 测试集比例
        """
        self.segment_length = segment_length
        self.step_size = step_size
        self.mode = mode

        # 加载并处理数据
        data, labels = self.load_cwru_data(data_path)

        # 标准化数据
        self.scaler = StandardScaler()
        data = self.scaler.fit_transform(data)

        # 编码标签
        self.label_encoder = LabelEncoder()
        labels = self.label_encoder.fit_transform(labels)

        # 分割数据集
        X_train, X_test, y_train, y_test = train_test_split(
            data, labels, test_size=test_size, random_state=42, stratify=labels
        )

        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
        )

        # 根据模式选择数据
        if mode == 'train':
            self.data = X_train
            self.labels = y_train
        elif mode == 'val':
            self.data = X_val
            self.labels = y_val
        else:  # test
            self.data = X_test
            self.labels = y_test

        print(f"Loaded {len(self.data)} samples for {mode} set")

    def load_cwru_data(self, data_path):
        """加载CWRU数据集"""
        # 这里需要根据实际的数据文件结构进行调整
        # 以下是一个示例实现

        data = []
        labels = []

        # 假设数据按照故障类型组织在子文件夹中
        fault_types = ['Normal', 'IR', 'OR', 'Ball']  # 内圈、外圈、滚珠故障
        fault_depths = ['007', '014', '021']  # 故障深度 (英寸)

        # 加载正常数据
        normal_files = [f for f in os.listdir(data_path) if 'normal' in f.lower()]
        for file in normal_files:
            mat_data = scipy.io.loadmat(os.path.join(data_path, file))
            # 提取振动信号 (键名需要根据实际MAT文件调整)
            key = [k for k in mat_data.keys() if not k.startswith('__')][0]
            signal = mat_data[key].flatten()

            # 分割信号
            for i in range(0, len(signal) - self.segment_length, self.step_size):
                segment = signal[i:i + self.segment_length]
                data.append(segment)
                labels.append('Normal')

        # 加载故障数据 (简化实现)
        for fault_type in fault_types[1:]:
            for depth in fault_depths:
                fault_files = [f for f in os.listdir(data_path) if fault_type.lower() in f.lower() and depth in f]
                for file in fault_files:
                    mat_data = scipy.io.loadmat(os.path.join(data_path, file))
                    key = [k for k in mat_data.keys() if not k.startswith('__')][0]
                    signal = mat_data[key].flatten()

                    for i in range(0, len(signal) - self.segment_length, self.step_size):
                        segment = signal[i:i + self.segment_length]
                        data.append(segment)
                        labels.append(f"{fault_type}_{depth}")

        return np.array(data), np.array(labels)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        # 将信号转换为适合CNN输入的格式 (通道, 长度)
        signal = self.data[idx].reshape(1, -1)
        label = self.labels[idx]
        return torch.FloatTensor(signal), torch.tensor(label, dtype=torch.long)


def get_data_loaders(data_path, batch_size=32, segment_length=1024, step_size=256):
    """创建训练、验证和测试数据加载器"""
    train_dataset = CWRUBearingDataset(data_path, segment_length, step_size, mode='train')
    val_dataset = CWRUBearingDataset(data_path, segment_length, step_size, mode='val')
    test_dataset = CWRUBearingDataset(data_path, segment_length, step_size, mode='test')

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    return train_loader, val_loader, test_loader