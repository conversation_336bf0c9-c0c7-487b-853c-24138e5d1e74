import numpy as np
import pandas as pd
import scipy.io
import os
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import Dataset, DataLoader
import warnings
warnings.filterwarnings('ignore')


class CWRUBearingDataset(Dataset):
    def __init__(self, data_path, segment_length=1024, step_size=256, mode='train',
                 test_size=0.2, sensor_type='DE', normalize=True, random_state=42):
        """
        加载CWRU轴承数据集
        :param data_path: 数据目录路径 (应该指向CRWU文件夹)
        :param segment_length: 信号段长度
        :param step_size: 滑动步长
        :param mode: 'train', 'val' 或 'test'
        :param test_size: 测试集比例
        :param sensor_type: 传感器类型 ('DE': 驱动端, 'FE': 风扇端, 'BA': 基座)
        :param normalize: 是否标准化数据
        :param random_state: 随机种子
        """
        self.segment_length = segment_length
        self.step_size = step_size
        self.mode = mode
        self.sensor_type = sensor_type
        self.normalize = normalize
        self.random_state = random_state

        # 加载并处理数据
        data, labels, self.class_names = self.load_cwru_data(data_path)

        print(f"原始数据形状: {data.shape}")
        print(f"标签分布: {np.unique(labels, return_counts=True)}")

        # 确保数据是2D格式
        if len(data.shape) == 1:
            data = data.reshape(-1, 1)
        elif len(data.shape) > 2:
            data = data.reshape(data.shape[0], -1)

        print(f"重塑后数据形状: {data.shape}")

        # 标准化数据
        if self.normalize:
            self.scaler = StandardScaler()
            data = self.scaler.fit_transform(data)
        else:
            self.scaler = None

        # 编码标签
        self.label_encoder = LabelEncoder()
        labels_encoded = self.label_encoder.fit_transform(labels)

        # 创建标签映射
        self.label_mapping = dict(zip(self.label_encoder.classes_,
                                    range(len(self.label_encoder.classes_))))
        print(f"标签映射: {self.label_mapping}")

        # 检查每个类别的样本数量
        unique_labels, label_counts = np.unique(labels_encoded, return_counts=True)
        min_samples = np.min(label_counts)
        print(f"最少样本数的类别有 {min_samples} 个样本")

        # 如果样本数太少，不使用分层抽样
        use_stratify = min_samples >= 2  # 至少需要2个样本才能分层

        # 分割数据集
        if use_stratify:
            X_train, X_test, y_train, y_test = train_test_split(
                data, labels_encoded, test_size=test_size, random_state=random_state,
                stratify=labels_encoded
            )
        else:
            print("样本数太少，不使用分层抽样")
            X_train, X_test, y_train, y_test = train_test_split(
                data, labels_encoded, test_size=test_size, random_state=random_state
            )

        # 检查训练集中每个类别的样本数
        unique_train_labels, train_label_counts = np.unique(y_train, return_counts=True)
        min_train_samples = np.min(train_label_counts)
        use_stratify_val = min_train_samples >= 2

        if use_stratify_val:
            X_train, X_val, y_train, y_val = train_test_split(
                X_train, y_train, test_size=0.2, random_state=random_state,
                stratify=y_train
            )
        else:
            print("训练集样本数太少，验证集分割不使用分层抽样")
            X_train, X_val, y_train, y_val = train_test_split(
                X_train, y_train, test_size=0.2, random_state=random_state
            )

        # 根据模式选择数据
        if mode == 'train':
            self.data = X_train
            self.labels = y_train
        elif mode == 'val':
            self.data = X_val
            self.labels = y_val
        else:  # test
            self.data = X_test
            self.labels = y_test

        print(f"加载了 {len(self.data)} 个样本用于 {mode} 集")
        print(f"数据形状: {self.data.shape}")
        print(f"标签形状: {self.labels.shape}")
        print(f"类别数量: {len(np.unique(self.labels))}")

    def load_cwru_data(self, data_path):
        """
        加载CWRU数据集
        根据实际的文件结构加载数据
        """
        data = []
        labels = []
        class_names = []

        print(f"从路径加载数据: {data_path}")

        # 1. 加载正常数据
        normal_path = os.path.join(data_path, "Normal Baseline")
        if os.path.exists(normal_path):
            print("加载正常数据...")
            normal_files = [f for f in os.listdir(normal_path) if f.endswith('.mat')]

            for file in normal_files:
                file_path = os.path.join(normal_path, file)
                try:
                    mat_data = scipy.io.loadmat(file_path)

                    # 根据传感器类型选择信号
                    signal_key = self._get_signal_key(mat_data, self.sensor_type)
                    if signal_key:
                        signal = mat_data[signal_key].flatten()
                        segments, segment_labels = self._segment_signal(signal, 'Normal')
                        data.extend(segments)
                        labels.extend(segment_labels)

                except Exception as e:
                    print(f"加载文件 {file} 时出错: {e}")
                    continue

            if 'Normal' not in class_names:
                class_names.append('Normal')

        # 2. 加载故障数据
        fault_base_path = os.path.join(data_path, "12k Drive End Bearing Fault Data")
        if os.path.exists(fault_base_path):
            print("加载故障数据...")

            # 内圈故障
            self._load_fault_data(fault_base_path, "Inner Race", "IR", data, labels, class_names)

            # 外圈故障
            self._load_fault_data(fault_base_path, "Outer Race", "OR", data, labels, class_names)

            # 滚珠故障
            self._load_fault_data(fault_base_path, "Ball", "Ball", data, labels, class_names)

        print(f"总共加载了 {len(data)} 个数据段")
        print(f"类别: {class_names}")

        return np.array(data), np.array(labels), class_names

    def _get_signal_key(self, mat_data, sensor_type):
        """根据传感器类型获取信号键名"""
        keys = [k for k in mat_data.keys() if not k.startswith('__')]

        for key in keys:
            if sensor_type == 'DE' and '_DE_time' in key:
                return key
            elif sensor_type == 'FE' and '_FE_time' in key:
                return key
            elif sensor_type == 'BA' and '_BA_time' in key:
                return key

        # 如果没找到指定类型，返回第一个时间序列数据
        for key in keys:
            if 'time' in key and not 'RPM' in key:
                return key

        return None

    def _segment_signal(self, signal, label):
        """将信号分割成固定长度的段"""
        segments = []
        segment_labels = []

        for i in range(0, len(signal) - self.segment_length + 1, self.step_size):
            segment = signal[i:i + self.segment_length]
            segments.append(segment)
            segment_labels.append(label)

        return segments, segment_labels

    def _load_fault_data(self, base_path, fault_type_folder, fault_prefix, data, labels, class_names):
        """加载特定类型的故障数据"""
        fault_path = os.path.join(base_path, fault_type_folder)

        if not os.path.exists(fault_path):
            print(f"路径不存在: {fault_path}")
            return

        # 遍历故障深度文件夹
        for depth_folder in os.listdir(fault_path):
            depth_path = os.path.join(fault_path, depth_folder)

            if os.path.isdir(depth_path):
                print(f"加载 {fault_type_folder} - {depth_folder} 数据...")

                # 创建标签
                if fault_type_folder == "Outer Race":
                    # 外圈故障有不同位置 (Centered, Opposite, Orthogonal)
                    label = f"{fault_prefix}_{depth_folder}"
                else:
                    label = f"{fault_prefix}_{depth_folder}"

                if label not in class_names:
                    class_names.append(label)

                # 加载该深度下的所有文件
                mat_files = [f for f in os.listdir(depth_path) if f.endswith('.mat')]

                for file in mat_files:
                    file_path = os.path.join(depth_path, file)
                    try:
                        mat_data = scipy.io.loadmat(file_path)
                        signal_key = self._get_signal_key(mat_data, self.sensor_type)

                        if signal_key:
                            signal = mat_data[signal_key].flatten()
                            segments, segment_labels = self._segment_signal(signal, label)
                            data.extend(segments)
                            labels.extend(segment_labels)

                    except Exception as e:
                        print(f"加载文件 {file} 时出错: {e}")
                        continue

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        # 将信号转换为适合CNN输入的格式 (通道, 长度)
        signal = self.data[idx].reshape(1, -1)
        label = self.labels[idx]
        return torch.FloatTensor(signal), torch.tensor(label, dtype=torch.long)

    def get_class_names(self):
        """获取类别名称"""
        return self.class_names

    def get_label_mapping(self):
        """获取标签映射"""
        return self.label_mapping

    def get_num_classes(self):
        """获取类别数量"""
        return len(self.class_names)


def get_data_loaders(data_path, batch_size=32, segment_length=1024, step_size=256,
                    sensor_type='DE', normalize=True, test_size=0.2, random_state=42):
    """
    创建训练、验证和测试数据加载器

    :param data_path: 数据路径 (指向CRWU文件夹)
    :param batch_size: 批次大小
    :param segment_length: 信号段长度
    :param step_size: 滑动步长
    :param sensor_type: 传感器类型 ('DE', 'FE', 'BA')
    :param normalize: 是否标准化
    :param test_size: 测试集比例
    :param random_state: 随机种子
    :return: train_loader, val_loader, test_loader, class_names, num_classes
    """
    print("创建数据加载器...")

    # 创建数据集 (使用相同的参数确保一致性)
    train_dataset = CWRUBearingDataset(
        data_path, segment_length, step_size, mode='train',
        test_size=test_size, sensor_type=sensor_type,
        normalize=normalize, random_state=random_state
    )

    val_dataset = CWRUBearingDataset(
        data_path, segment_length, step_size, mode='val',
        test_size=test_size, sensor_type=sensor_type,
        normalize=normalize, random_state=random_state
    )

    test_dataset = CWRUBearingDataset(
        data_path, segment_length, step_size, mode='test',
        test_size=test_size, sensor_type=sensor_type,
        normalize=normalize, random_state=random_state
    )

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True,
        num_workers=0, pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False,
        num_workers=0, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, shuffle=False,
        num_workers=0, pin_memory=True
    )

    # 获取类别信息
    class_names = train_dataset.get_class_names()
    num_classes = train_dataset.get_num_classes()

    print(f"数据加载器创建完成:")
    print(f"  训练集: {len(train_dataset)} 样本")
    print(f"  验证集: {len(val_dataset)} 样本")
    print(f"  测试集: {len(test_dataset)} 样本")
    print(f"  类别数: {num_classes}")
    print(f"  类别名: {class_names}")

    return train_loader, val_loader, test_loader, class_names, num_classes


def analyze_dataset(data_path, sensor_type='DE'):
    """
    分析数据集的基本信息
    """
    print("=== 数据集分析 ===")

    # 创建一个临时数据集来分析
    temp_dataset = CWRUBearingDataset(
        data_path, segment_length=1024, step_size=512,
        mode='train', sensor_type=sensor_type
    )

    print(f"\n传感器类型: {sensor_type}")
    print(f"总样本数: {len(temp_dataset)}")
    print(f"信号段长度: {temp_dataset.segment_length}")
    print(f"滑动步长: {temp_dataset.step_size}")

    # 分析每个类别的样本数
    unique_labels, counts = np.unique(temp_dataset.labels, return_counts=True)
    class_names = temp_dataset.get_class_names()

    print(f"\n类别分布:")
    for i, (label_idx, count) in enumerate(zip(unique_labels, counts)):
        class_name = class_names[label_idx] if label_idx < len(class_names) else f"Class_{label_idx}"
        print(f"  {class_name}: {count} 样本")

    # 分析信号统计信息
    sample_data = temp_dataset.data[:1000]  # 取前1000个样本分析
    print(f"\n信号统计信息 (基于前1000个样本):")
    print(f"  均值: {np.mean(sample_data):.6f}")
    print(f"  标准差: {np.std(sample_data):.6f}")
    print(f"  最小值: {np.min(sample_data):.6f}")
    print(f"  最大值: {np.max(sample_data):.6f}")

    return temp_dataset


if __name__ == "__main__":
    # 测试数据加载器
    data_path = "diagnose_model/data/CRWU"

    # 分析数据集
    dataset = analyze_dataset(data_path, sensor_type='DE')

    # 创建数据加载器
    train_loader, val_loader, test_loader, class_names, num_classes = get_data_loaders(
        data_path, batch_size=32, segment_length=1024, step_size=256
    )

    # 测试数据加载
    print("\n=== 测试数据加载 ===")
    for i, (data, labels) in enumerate(train_loader):
        print(f"批次 {i+1}:")
        print(f"  数据形状: {data.shape}")
        print(f"  标签形状: {labels.shape}")
        print(f"  标签范围: {labels.min().item()} - {labels.max().item()}")
        if i >= 2:  # 只测试前3个批次
            break