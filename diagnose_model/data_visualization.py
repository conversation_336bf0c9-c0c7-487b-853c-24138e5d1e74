import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
from scipy.fft import fft, fftfreq
import pandas as pd
from data_loader import CWRUBearingDataset
import os

plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号


class DataVisualizer:
    def __init__(self, data_path, sensor_type='DE'):
        """
        数据可视化工具
        
        :param data_path: 数据路径
        :param sensor_type: 传感器类型
        """
        self.data_path = data_path
        self.sensor_type = sensor_type
        
        # 创建数据集实例
        self.dataset = CWRUBearingDataset(
            data_path, segment_length=1024, step_size=512, 
            mode='train', sensor_type=sensor_type
        )
        
    def plot_signal_samples(self, num_samples=3, save_path=None):
        """
        绘制每个类别的信号样本
        
        :param num_samples: 每个类别显示的样本数
        :param save_path: 保存路径
        """
        class_names = self.dataset.get_class_names()
        num_classes = len(class_names)
        
        fig, axes = plt.subplots(num_classes, num_samples, 
                                figsize=(15, 3*num_classes))
        
        if num_classes == 1:
            axes = axes.reshape(1, -1)
        if num_samples == 1:
            axes = axes.reshape(-1, 1)
            
        for class_idx, class_name in enumerate(class_names):
            # 找到该类别的样本索引
            class_indices = np.where(self.dataset.labels == class_idx)[0]
            
            # 随机选择样本
            selected_indices = np.random.choice(class_indices, 
                                              min(num_samples, len(class_indices)), 
                                              replace=False)
            
            for sample_idx, data_idx in enumerate(selected_indices):
                signal_data = self.dataset.data[data_idx]
                
                axes[class_idx, sample_idx].plot(signal_data)
                axes[class_idx, sample_idx].set_title(f'{class_name} - 样本 {sample_idx+1}')
                axes[class_idx, sample_idx].set_xlabel('时间点')
                axes[class_idx, sample_idx].set_ylabel('振幅')
                axes[class_idx, sample_idx].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"信号样本图已保存到: {save_path}")
        
        plt.show()
    
    def plot_frequency_spectrum(self, num_samples=1, sampling_rate=12000, save_path=None):
        """
        绘制频谱分析图
        
        :param num_samples: 每个类别的样本数
        :param sampling_rate: 采样率
        :param save_path: 保存路径
        """
        class_names = self.dataset.get_class_names()
        num_classes = len(class_names)
        
        fig, axes = plt.subplots(num_classes, 2, figsize=(15, 4*num_classes))
        
        if num_classes == 1:
            axes = axes.reshape(1, -1)
            
        for class_idx, class_name in enumerate(class_names):
            # 找到该类别的样本
            class_indices = np.where(self.dataset.labels == class_idx)[0]
            selected_idx = np.random.choice(class_indices)
            signal_data = self.dataset.data[selected_idx]
            
            # 时域信号
            axes[class_idx, 0].plot(signal_data)
            axes[class_idx, 0].set_title(f'{class_name} - 时域信号')
            axes[class_idx, 0].set_xlabel('时间点')
            axes[class_idx, 0].set_ylabel('振幅')
            axes[class_idx, 0].grid(True, alpha=0.3)
            
            # 频域信号
            N = len(signal_data)
            yf = fft(signal_data)
            xf = fftfreq(N, 1/sampling_rate)
            
            # 只显示正频率部分
            axes[class_idx, 1].plot(xf[:N//2], np.abs(yf[:N//2]))
            axes[class_idx, 1].set_title(f'{class_name} - 频域信号')
            axes[class_idx, 1].set_xlabel('频率 (Hz)')
            axes[class_idx, 1].set_ylabel('幅度')
            axes[class_idx, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"频谱分析图已保存到: {save_path}")
        
        plt.show()
    
    def plot_class_distribution(self, save_path=None):
        """
        绘制类别分布图
        
        :param save_path: 保存路径
        """
        class_names = self.dataset.get_class_names()
        unique_labels, counts = np.unique(self.dataset.labels, return_counts=True)
        
        # 创建类别名称列表
        class_labels = [class_names[i] for i in unique_labels]
        
        plt.figure(figsize=(12, 6))
        
        # 柱状图
        plt.subplot(1, 2, 1)
        bars = plt.bar(class_labels, counts, color='skyblue', alpha=0.7)
        plt.title('类别分布 - 柱状图')
        plt.xlabel('故障类型')
        plt.ylabel('样本数量')
        plt.xticks(rotation=45, ha='right')
        
        # 在柱子上添加数值
        for bar, count in zip(bars, counts):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                    str(count), ha='center', va='bottom')
        
        # 饼图
        plt.subplot(1, 2, 2)
        plt.pie(counts, labels=class_labels, autopct='%1.1f%%', startangle=90)
        plt.title('类别分布 - 饼图')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"类别分布图已保存到: {save_path}")
        
        plt.show()
    
    def plot_statistical_analysis(self, save_path=None):
        """
        绘制统计分析图
        
        :param save_path: 保存路径
        """
        class_names = self.dataset.get_class_names()
        
        # 计算每个类别的统计特征
        stats_data = []
        
        for class_idx, class_name in enumerate(class_names):
            class_indices = np.where(self.dataset.labels == class_idx)[0]
            class_signals = self.dataset.data[class_indices]
            
            # 计算统计特征
            mean_vals = np.mean(class_signals, axis=1)
            std_vals = np.std(class_signals, axis=1)
            max_vals = np.max(class_signals, axis=1)
            min_vals = np.min(class_signals, axis=1)
            rms_vals = np.sqrt(np.mean(class_signals**2, axis=1))
            
            stats_data.append({
                'class': class_name,
                'mean': mean_vals,
                'std': std_vals,
                'max': max_vals,
                'min': min_vals,
                'rms': rms_vals
            })
        
        # 绘制箱线图
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        axes = axes.flatten()
        
        features = ['mean', 'std', 'max', 'min', 'rms']
        feature_names = ['均值', '标准差', '最大值', '最小值', 'RMS']
        
        for i, (feature, feature_name) in enumerate(zip(features, feature_names)):
            data_to_plot = [stats_data[j][feature] for j in range(len(class_names))]
            
            axes[i].boxplot(data_to_plot, labels=class_names)
            axes[i].set_title(f'{feature_name} 分布')
            axes[i].set_ylabel(feature_name)
            axes[i].tick_params(axis='x', rotation=45)
            axes[i].grid(True, alpha=0.3)
        
        # 删除多余的子图
        axes[5].remove()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"统计分析图已保存到: {save_path}")
        
        plt.show()
    
    def plot_correlation_matrix(self, save_path=None):
        """
        绘制特征相关性矩阵
        
        :param save_path: 保存路径
        """
        # 计算每个信号的统计特征
        features = []
        labels = []
        
        for i in range(len(self.dataset)):
            signal_data = self.dataset.data[i]
            
            # 提取特征
            feature_vector = [
                np.mean(signal_data),      # 均值
                np.std(signal_data),       # 标准差
                np.max(signal_data),       # 最大值
                np.min(signal_data),       # 最小值
                np.sqrt(np.mean(signal_data**2)),  # RMS
                np.var(signal_data),       # 方差
                np.median(signal_data),    # 中位数
                np.percentile(signal_data, 25),  # 25%分位数
                np.percentile(signal_data, 75),  # 75%分位数
            ]
            
            features.append(feature_vector)
            labels.append(self.dataset.labels[i])
        
        # 创建DataFrame
        feature_names = ['均值', '标准差', '最大值', '最小值', 'RMS', 
                        '方差', '中位数', '25%分位数', '75%分位数']
        
        df = pd.DataFrame(features, columns=feature_names)
        df['标签'] = labels
        
        # 计算相关性矩阵
        correlation_matrix = df[feature_names].corr()
        
        # 绘制热力图
        plt.figure(figsize=(10, 8))
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, fmt='.2f')
        plt.title('特征相关性矩阵')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"相关性矩阵图已保存到: {save_path}")
        
        plt.show()
    
    def generate_full_report(self, output_dir='visualizations'):
        """
        生成完整的数据分析报告
        
        :param output_dir: 输出目录
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        print("生成数据可视化报告...")
        
        # 1. 信号样本
        print("1. 生成信号样本图...")
        self.plot_signal_samples(num_samples=3, 
                                save_path=os.path.join(output_dir, 'signal_samples.png'))
        
        # 2. 频谱分析
        print("2. 生成频谱分析图...")
        self.plot_frequency_spectrum(save_path=os.path.join(output_dir, 'frequency_spectrum.png'))
        
        # 3. 类别分布
        print("3. 生成类别分布图...")
        self.plot_class_distribution(save_path=os.path.join(output_dir, 'class_distribution.png'))
        
        # 4. 统计分析
        print("4. 生成统计分析图...")
        self.plot_statistical_analysis(save_path=os.path.join(output_dir, 'statistical_analysis.png'))
        
        # 5. 相关性矩阵
        print("5. 生成相关性矩阵图...")
        self.plot_correlation_matrix(save_path=os.path.join(output_dir, 'correlation_matrix.png'))
        
        print(f"数据可视化报告已生成完成，保存在: {output_dir}")


if __name__ == "__main__":
    # 创建可视化工具
    data_path = "./data/CRWU"
    visualizer = DataVisualizer(data_path, sensor_type='DE')
    
    # 生成完整报告
    visualizer.generate_full_report()
