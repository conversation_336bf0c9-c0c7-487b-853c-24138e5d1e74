import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns
from data_loader import get_data_loaders
from models import CNN1DClassifier


def evaluate_model(data_path, model_path, segment_length=1024, num_classes=4):
    """评估模型性能"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 加载测试数据
    _, _, test_loader = get_data_loaders(data_path, batch_size=32, segment_length=segment_length)

    # 初始化模型
    model = CNN1DClassifier(num_classes=num_classes)
    model.to(device)

    # 加载训练好的模型
    checkpoint = torch.load(model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    # 进行预测
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            _, preds = torch.max(output, 1)

            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(target.cpu().numpy())

    # 计算评估指标
    accuracy = 100.0 * np.sum(np.array(all_preds) == np.array(all_labels)) / len(all_labels)
    print(f"Test Accuracy: {accuracy:.2f}%")

    # 生成分类报告
    print("\nClassification Report:")
    print(classification_report(all_labels, all_preds))

    # 绘制混淆矩阵
    cm = confusion_matrix(all_labels, all_preds)
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.savefig('confusion_matrix.png')
    plt.show()

    return accuracy


if __name__ == "__main__":
    data_path = "./data"
    model_path = "./runs/exp/best_model.pth"
    evaluate_model(data_path, model_path)