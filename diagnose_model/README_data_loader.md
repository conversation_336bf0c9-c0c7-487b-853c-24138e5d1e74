# CWRU轴承故障数据加载器详细实现

## 概述

本项目提供了一套完整的CWRU轴承故障数据集加载和处理工具，包括基础数据加载器、高级数据加载器（支持数据增强）、数据可视化工具和使用示例。

## 文件结构

```
diagnose_model/
├── data_loader.py              # 基础数据加载器
├── advanced_data_loader.py     # 高级数据加载器（支持数据增强）
├── data_visualization.py       # 数据可视化工具
├── data_loader_example.py      # 使用示例
└── README_data_loader.md       # 本文档
```

## 数据集结构

CWRU数据集应按以下结构组织：

```
./data/CRWU/
├── Normal Baseline/
│   ├── normal_0.mat
│   ├── normal_1.mat
│   └── ...
└── 12k Drive End Bearing Fault Data/
    ├── Inner Race/
    │   ├── 0007/
    │   │   ├── IR007_0.mat
    │   │   └── ...
    │   ├── 0014/
    │   ├── 0021/
    │   └── 0028/
    ├── Outer Race/
    │   ├── Centered/
    │   ├── Opposite/
    │   └── Orthogonal/
    └── Ball/
        ├── 0007/
        ├── 0014/
        ├── 0021/
        └── 0028/
```

## 主要功能

### 1. 基础数据加载器 (`data_loader.py`)

**特点：**
- 支持多种传感器类型（DE、FE、BA）
- 自动信号分割和标签生成
- 数据标准化
- 训练/验证/测试集自动分割

**使用示例：**
```python
from data_loader import get_data_loaders

train_loader, val_loader, test_loader, class_names, num_classes = get_data_loaders(
    data_path="./data/CRWU",
    batch_size=32,
    segment_length=1024,
    step_size=256,
    sensor_type='DE',
    normalize=True
)
```

### 2. 高级数据加载器 (`advanced_data_loader.py`)

**特点：**
- 支持多种数据增强技术
  - 高斯噪声添加
  - 时间偏移
  - 幅度缩放
- 多种标准化方法（standard、minmax、none）
- 只在训练集使用数据增强

**使用示例：**
```python
from advanced_data_loader import get_advanced_data_loaders

train_loader, val_loader, test_loader, class_names, num_classes = get_advanced_data_loaders(
    data_path="./data/CRWU",
    batch_size=32,
    segment_length=1024,
    step_size=256,
    sensor_type='DE',
    normalize_method='standard',
    augmentation=True,
    noise_level=0.01
)
```

### 3. 数据可视化工具 (`data_visualization.py`)

**功能：**
- 信号样本可视化
- 频谱分析
- 类别分布统计
- 统计特征分析
- 特征相关性分析

**使用示例：**
```python
from data_visualization import DataVisualizer

visualizer = DataVisualizer("./data/CRWU", sensor_type='DE')
visualizer.generate_full_report(output_dir='visualizations')
```

## 支持的故障类型

数据加载器自动识别以下故障类型：

1. **Normal** - 正常状态
2. **IR_0007** - 内圈故障 (0.007英寸)
3. **IR_0014** - 内圈故障 (0.014英寸)
4. **IR_0021** - 内圈故障 (0.021英寸)
5. **IR_0028** - 内圈故障 (0.028英寸)
6. **OR_Centered** - 外圈故障 (中心位置)
7. **OR_Opposite** - 外圈故障 (对面位置)
8. **OR_Orthogonal** - 外圈故障 (正交位置)
9. **Ball_0007** - 滚珠故障 (0.007英寸)
10. **Ball_0014** - 滚珠故障 (0.014英寸)
11. **Ball_0021** - 滚珠故障 (0.021英寸)
12. **Ball_0028** - 滚珠故障 (0.028英寸)

## 数据增强技术

高级数据加载器支持以下数据增强技术：

### 1. 高斯噪声添加
```python
def _add_noise(self, signal, noise_level):
    noise = np.random.normal(0, noise_level * np.std(signal), signal.shape)
    return signal + noise
```

### 2. 时间偏移
```python
def _time_shift(self, signal, max_shift=50):
    shift = np.random.randint(-max_shift, max_shift)
    # 循环偏移实现
```

### 3. 幅度缩放
```python
def _amplitude_scale(self, signal, scale_range=(0.8, 1.2)):
    scale = np.random.uniform(scale_range[0], scale_range[1])
    return signal * scale
```

## 配置参数说明

### 基础参数
- `data_path`: 数据路径，指向CRWU文件夹
- `batch_size`: 批次大小，默认32
- `segment_length`: 信号段长度，默认1024
- `step_size`: 滑动步长，默认256
- `sensor_type`: 传感器类型，'DE'(驱动端)、'FE'(风扇端)、'BA'(基座)
- `test_size`: 测试集比例，默认0.2
- `random_state`: 随机种子，默认42

### 高级参数
- `normalize_method`: 标准化方法，'standard'、'minmax'、'none'
- `augmentation`: 是否使用数据增强，默认True
- `noise_level`: 噪声水平，默认0.01

## 性能优化

1. **并行数据加载**: 使用PyTorch的DataLoader进行多进程加载
2. **内存优化**: 支持不同的批次大小以适应内存限制
3. **缓存机制**: 数据预处理后缓存，避免重复计算

## 使用建议

### 1. 信号段长度选择
- **短段(512)**: 适合快速训练，但可能丢失长期依赖信息
- **中段(1024)**: 平衡性能和信息保留，推荐使用
- **长段(2048)**: 保留更多信息，但计算开销大

### 2. 步长选择
- **小步长(128-256)**: 更多重叠，数据量大，训练时间长
- **大步长(512-1024)**: 较少重叠，数据量适中，训练快速

### 3. 数据增强策略
- **轻度增强**: noise_level=0.01, 适合数据充足的情况
- **中度增强**: noise_level=0.02-0.03, 适合一般情况
- **重度增强**: noise_level=0.05+, 适合数据稀少的情况

## 故障排除

### 常见问题

1. **文件路径错误**
   ```
   FileNotFoundError: 找不到数据文件
   ```
   解决：检查数据路径是否正确，确保指向CRWU文件夹

2. **内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   解决：减小batch_size或segment_length

3. **数据不平衡**
   ```
   某些类别样本数量过少
   ```
   解决：使用数据增强或调整step_size

### 调试技巧

1. **使用分析函数**:
   ```python
   from data_loader import analyze_dataset
   dataset = analyze_dataset("./data/CRWU")
   ```

2. **检查数据形状**:
   ```python
   for data, labels in train_loader:
       print(f"数据形状: {data.shape}, 标签形状: {labels.shape}")
       break
   ```

3. **可视化数据**:
   ```python
   from data_visualization import DataVisualizer
   visualizer = DataVisualizer("./data/CRWU")
   visualizer.plot_signal_samples()
   ```

## 扩展功能

### 1. 添加新的故障类型
在`_load_fault_data`方法中添加新的故障类型处理逻辑

### 2. 自定义数据增强
在`AdvancedCWRUBearingDataset`类中添加新的增强方法

### 3. 支持其他数据格式
修改`_get_signal_key`方法以支持不同的MAT文件格式

## 完整使用示例

```python
# 运行完整示例
python diagnose_model/data_loader_example.py

# 生成数据可视化报告
python diagnose_model/data_visualization.py
```

这将运行所有功能的演示，包括基础加载、高级加载、性能测试和可视化。
